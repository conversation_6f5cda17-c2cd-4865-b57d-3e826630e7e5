.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-dialog {
  width: 420px;
  background: var(--color-bg-dialog);
  border-radius: var(--radius-lg);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  display: flex;
  flex-direction: column;
  /* 移除顶部内边距，使 header 紧贴对话框顶部；
     其余位置保持原有间距 */
  padding: 0 16px 16px;
  color: var(--color-content-regular);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--font-size-lg);
  margin-bottom: 16px;
}

.modal-dialog > .modal-header {
  padding: 16px 0;
  border-bottom: none;
}

.close-icon {
  cursor: pointer;
}

.upload-area {
  height: 180px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  cursor: pointer;
  border-radius: var(--radius-lg);
}
.upload-area.drag-over {
  background-color: var(--color-bg-hover);
}
.upload-area p {
  margin: 8px 0 0;
  font-size: var(--font-size-base);
  color: var(--color-content-regular);
}
.upload-area:hover {
  background-color: var(--color-bg-hover);
  transition: all 0.3s ease;
}
.file-name {
  color: var(--color-primary);
  font-weight: var(--font-weight-medium);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}