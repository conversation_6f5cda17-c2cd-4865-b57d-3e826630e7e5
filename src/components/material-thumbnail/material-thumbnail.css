.material-item {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-base);
  overflow: hidden;
  cursor: pointer;
  position: relative;
  background: var(--color-bg-overlay, rgba(255, 255, 255, 0.05));
  outline: 1px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-in-out;
  /* 确保材质项目在网格单元格中居中显示 */
  margin: 0 auto;
}

.material-item:hover {
  background: var(--color-bg-hover, rgba(255, 255, 255, 0.1));
}

.thumbnail-canvas {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  overflow: hidden;
}

.material-item canvas {
  width: 100% !important;
  height: 100% !important;
  display: block;
}

.material-item.active {
  outline: 1px solid var(--color-brand, #2269EC);
}

.material-item.active:hover {
  background: var(--color-bg-hover, rgba(255, 255, 255, 0.1));
}