.model-management {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.management-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.toolbar-actions {
  display: flex;
  gap: 12px;
}

.models-table-container {
  overflow-x: auto;
  background-color: var(--color-bg-dialog);
  border-radius: var(--radius-m);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.models-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.models-table th,
.models-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--color-border);
  vertical-align: middle;
}

.models-table th {
  font-weight: 500;
  color: var(--color-content-regular);
  background-color: var(--color-bg-primary);
  white-space: nowrap;
}

.models-table tbody tr:hover {
  background-color: var(--color-bg-overlay);
}

/* 缩略图单元格 */
.model-thumbnail {
  width: 60px;
  padding: 8px !important;
  text-align: center;
}

.thumbnail-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  background-color: var(--color-bg-input);
}

.thumbnail-placeholder {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-bg-input);
  border-radius: 4px;
  color: var(--color-content-mute);
}

.model-name {
  font-weight: 500;
  color: var(--color-content-accent);
  min-width: 150px;
}

.file-type-badge {
  display: inline-block;
  padding: 2px 8px;
  background-color: var(--color-support);
  border-radius: var(--radius-xs);
  font-size: 14px;
  color: var(--color-content-secondary);
}

.file-size {
  color: var(--color-content-regular);
  white-space: nowrap;
}

.model-date {
  color: var(--color-content-mute);
  white-space: nowrap;
}

.model-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-container {
  background-color: var(--color-dialog-background);
  border-radius: var(--radius-m);
  width: 500px;
  max-width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
}

.modal-header h2 {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-content-accent);
}

.modal-form {
  padding: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--color-content-regular);
}

.form-input {
  width: 100%;
  padding: 10px 12px;
  font-size: 14px;
  color: var(--color-content-accent);
  background-color: var(--color-bg-input);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-s);
  outline: none;
  transition: border-color 0.2s;
}

.form-input:focus {
  border-color: var(--color-brand);
}

.file-upload-area {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.file-input {
  display: none;
}

.file-info {
  font-size: 14px;
  color: var(--color-content-mute);
}

/* 上传模型拖拽区域样式 */
.upload-area {
  height: 160px;
  border: 1px dashed var(--color-border);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border-radius: var(--radius-m);
  cursor: pointer;
  text-align: center;
  gap: 8px;
  color: var(--color-content-regular);
  transition: background-color 0.2s ease;
}

.upload-area.drag-over {
  background-color: var(--color-bg-hover);
}

.upload-area p {
  margin: 0;
  font-size: var(--font-size-sm);
}

.file-name {
  color: var(--color-content-accent);
  font-weight: var(--font-weight-medium);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
}